﻿#,<PERSON><PERSON><PERSON> bà<PERSON>,<PERSON><PERSON>,<PERSON><PERSON><PERSON> testcase,<PERSON><PERSON><PERSON> độ ưu tiên,<PERSON><PERSON><PERSON> quả mong đợi
,,,,,
,<PERSON> getListDevice,,,,
1,<PERSON><PERSON><PERSON> API không truyền tham số (lấy toàn bộ danh sách mặc định) - <PERSON><PERSON><PERSON> khoản khách hàng root,<PERSON><PERSON><PERSON><PERSON> có,<PERSON><PERSON><PERSON> lệ,<PERSON>,"200 OK, trả về danh sách mặc định (trang đầu tiên) với dữ liệu danh sách thiết bị theo quyền tài khoản root được cấp"
2,G<PERSON><PERSON> API không truyền tham số (lấy toàn bộ danh sách mặc định) - <PERSON><PERSON><PERSON> kho<PERSON><PERSON> khách hàng mức con,<PERSON><PERSON><PERSON><PERSON> có,<PERSON><PERSON><PERSON> lệ,<PERSON>,"201 OK, trả về danh sách mặc định (trang đầu tiên) với dữ liệu danh sách thiết bị theo quyền tài khoản mức con đư<PERSON><PERSON> cấp"
3,<PERSON><PERSON><PERSON> theo msisdn hợp lệ,msisdn=0988123456,<PERSON><PERSON><PERSON>,<PERSON>,"200 OK, danh sách thiết bị có msisdn này"
4,Lọc theo imei hợp lệ,imei=123456789012345,Hợp lệ,Cao,"200 OK, trả về thiết bị ứng với imei"
5,Lọc theo location,location=Hanoi,Hợp lệ,Trung bình,"200 OK, danh sách thiết bị ở Hà Nội"
6,Lọc theo country,country=VN,Hợp lệ,Trung bình,"200 OK, thiết bị ở quốc gia VN"
7,Lọc theo deviceType,deviceType=smart_meter,Hợp lệ,Trung bình,"200 OK, chỉ có thiết bị loại smart_meter"
8,Lọc theo khoảng ngày hợp đồng,"contractDateFrom=2024-01-01, contractDateTo=2024-12-31",Hợp lệ,Trung bình,"200 OK, danh sách thiết bị trong khoảng thời gian"
9,Kết hợp nhiều bộ lọc,"msisdn=0988123456, deviceType=modem, country=VN",Hợp lệ,Trung bình,"200 OK, danh sách thiết bị đúng các điều kiện"
10,Phân trang,"page=1, size=10",Hợp lệ,Trung bình,"200 OK, trả về 10 bản ghi ở trang số 1"
11,Sắp xếp tăng dần,"sort=contractDate,asc",Hợp lệ,Thấp,"200 OK, danh sách được sắp theo ngày hợp đồng tăng"
12,Sắp xếp giảm dần,"sort=contractDate,desc",Hợp lệ,Thấp,"200 OK, danh sách được sắp theo ngày hợp đồng giảm"
13,API chưa được cấp quyền (cấu hình ko cấp quyền ở web),,Không hợp lệ,Cao,401 Unauthorized
14,sai token,,Không hợp lệ,Cao,402 Unauthorized
15,do dùng token cũ sau khi thực hiện API Register để generate token mới,,Không hợp lệ,Cao,402 Unauthorized
16,do dùng token cũ sau khi đổi Secret Key,,Không hợp lệ,Cao,402 Unauthorized
17,do cấp quyền API chuyển sang trạng thái Ngừng hoạt động,,Không hợp lệ,Cao,402 Unauthorized
18,do tài khoản khách hàng chuyển sang trạng thái Không hoạt động,,Không hợp lệ,Cao,402 Unauthorized
19,msisdn không được phân quyền,,Không hợp lệ,Cao,trả về lỗi
20,msisdn không tồn tại,,Không hợp lệ,Trung bình,trả về lỗi
21,msisdn sai định dạng,msisdn=abc123,Không hợp lệ,Trung bình,trả về lỗi
22,imei quá dài,imei=12345678901234567890,Không hợp lệ,Trung bình,trả về lỗi
23,imei không tồn tại,,Không hợp lệ,Trung bình,trả về lỗi
24,location chứa ký tự đặc biệt,location=@@@,Không hợp lệ,Trung bình,trả về lỗi
25,country không tồn tại,country=ZZ,Không hợp lệ,Thấp,trả về lỗi
26,deviceType sai định nghĩa,deviceType=unknown_type,Không hợp lệ,Thấp,trả về lỗi
27,contractDateFrom > contractDateTo,"contractDateFrom=2024-12-31, contractDateTo=2024-01-01",Không hợp lệ,Thấp,trả về lỗi
28,Ngày sai định dạng,contractDateFrom=2024/01/01,Không hợp lệ,Thấp,trả về lỗi
29,page âm,page=-1,Không hợp lệ,Thấp,trả về lỗi
30,size quá lớn,size=1000,Không hợp lệ,Thấp,trả về lỗi
31,sort sai định dạng,sort=contractDate-down,Không hợp lệ,Thấp,trả về lỗi
,API getDetailDevice,,,,
32,Gọi API với msisdn hợp lệ - Tài khoản khách hàng root,Không có,Hợp lệ,Cao,"200 OK, trả về với dữ liệu chi tiết thiết bị theo quyền tài khoản root được cấp"
33,Gọi API với msisdn hợp lệ - Tài khoản khách hàng mức con,Không có,Hợp lệ,Cao,"200 OK, trả về với dữ liệu chi tiết thiết bị theo quyền tài khoản mức con được cấp"
34,API chưa được cấp quyền (cấu hình ko cấp quyền ở web),,Không hợp lệ,Cao,401 Unauthorized
35,sai token,,Không hợp lệ,Cao,402 Unauthorized
36,do dùng token cũ sau khi thực hiện API Register để generate token mới,,Không hợp lệ,Cao,402 Unauthorized
37,do dùng token cũ sau khi đổi Secret Key,,Không hợp lệ,Cao,402 Unauthorized
38,do cấp quyền API chuyển sang trạng thái Ngừng hoạt động,,Không hợp lệ,Cao,402 Unauthorized
39,do tài khoản khách hàng chuyển sang trạng thái Không hoạt động,,Không hợp lệ,Cao,402 Unauthorized
40,msisdn không được phân quyền,,Không hợp lệ,Cao,trả về lỗi
41,msisdn không tồn tại,,Không hợp lệ,Trung bình,trả về lỗi
42,msisdn sai định dạng,msisdn=abc123,Không hợp lệ,Trung bình,trả về lỗi
,API getListCustomer,,,,
43,Gọi API không truyền tham số - Tài khoản khách hàng root,Không có,Hợp lệ,Cao,"200 OK, trả về trả về danh sách mặc định (trang đầu tiên) với dữ liệu danh sách khách hàng theo quyền tài khoản root được cấp"
44,Gọi API không truyền tham số - Tài khoản khách hàng mức con,Không có,Hợp lệ,Cao,"201 OK, trả về trả về danh sách mặc định (trang đầu tiên) với dữ liệu danh sách khách hàng theo quyền tài khoản mức con được cấp"
45,Lọc theo customerCode,customerCode=C12345,Hợp lệ,Cao,"200 OK, danh sách chứa customerCode tương ứng"
46,Lọc theo customerName,customerName=Nguyen Van A,Hợp lệ,Cao,"200 OK, danh sách chứa khách hàng có tên khớp"
47,Lọc theo customerType,customerType=individual,Hợp lệ,Trung bình,"200 OK, lọc đúng loại khách hàng"
48,Lọc theo taxId,taxId=1234567890,Hợp lệ,Trung bình,"200 OK, khách hàng có mã số thuế tương ứng"
49,Lọc theo phone,phone=**********,Hợp lệ,Trung bình,"200 OK, khách hàng có số điện thoại khớp"
50,Lọc theo email,email=<EMAIL>,Hợp lệ,Trung bình,"200 OK, khách hàng có email khớp"
51,Lọc theo status,status=active,Hợp lệ,Trung bình,"200 OK, khách hàng đang hoạt động"
52,Phân trang,"page=1, size=10",Hợp lệ,Thấp,"200 OK, danh sách gồm 10 bản ghi tại trang 1"
53,Sắp xếp tăng dần theo tên,"sort=customerName,asc",Hợp lệ,Thấp,"200 OK, danh sách sắp theo tên A-Z"
54,API chưa được cấp quyền (cấu hình ko cấp quyền ở web),,Không hợp lệ,Cao,401 Unauthorized
55,sai token,,Không hợp lệ,Cao,402 Unauthorized
56,do dùng token cũ sau khi thực hiện API Register để generate token mới,,Không hợp lệ,Cao,402 Unauthorized
57,do dùng token cũ sau khi đổi Secret Key,,Không hợp lệ,Cao,402 Unauthorized
58,do cấp quyền API chuyển sang trạng thái Ngừng hoạt động,,Không hợp lệ,Cao,402 Unauthorized
59,do tài khoản khách hàng chuyển sang trạng thái Không hoạt động,,Không hợp lệ,Cao,402 Unauthorized
60,customerCode không tồn tại,customerCode=C@123,Không hợp lệ,Trung bình,trả về lỗi
61,customerCode không được phân quyền,,Không hợp lệ,Trung bình,trả về lỗi
62,customerType không hợp lệ,customerType=alien,Không hợp lệ,Trung bình,trả về lỗi
63,email sai định dạng,email=test@@mail,Không hợp lệ,Thấp,trả về lỗi
64,phone sai định dạng,phone=abc123,Không hợp lệ,Thấp,trả về lỗi
65,status sai giá trị,status=unknown,Không hợp lệ,Thấp,trả về lỗi
66,sort sai định dạng,sort=name-up,Không hợp lệ,Thấp,trả về lỗi
67,page âm,page=-1,Không hợp lệ,Thấp,trả về lỗi
68,size quá lớn,size=10000,Không hợp lệ,Thấp,trả về lỗi
69,Tìm khách hàng không tồn tại,customerCode=NONEXIST,Không hợp lệ,Thấp,trả về lỗi
,API getDetailCustomer,,,,
70,Gọi API với customerCode hợp lệ - Tài khoản khách hàng root,Không có,Hợp lệ,Cao,"200 OK, trả về với dữ liệu chi tiết khách hàng theo quyền tài khoản root được cấp"
71,Gọi API với customerCode hợp lệ - Tài khoản khách hàng mức con,Không có,Hợp lệ,Cao,"200 OK, trả về với dữ liệu chi tiết khách hàng theo quyền tài khoản mức con được cấp"
72,API chưa được cấp quyền (cấu hình ko cấp quyền ở web),,Không hợp lệ,Cao,401 Unauthorized
73,sai token,,Không hợp lệ,Cao,402 Unauthorized
74,do dùng token cũ sau khi thực hiện API Register để generate token mới,,Không hợp lệ,Cao,402 Unauthorized
75,do dùng token cũ sau khi đổi Secret Key,,Không hợp lệ,Cao,402 Unauthorized
76,do cấp quyền API chuyển sang trạng thái Ngừng hoạt động,,Không hợp lệ,Cao,402 Unauthorized
77,do tài khoản khách hàng chuyển sang trạng thái Không hoạt động,,Không hợp lệ,Cao,402 Unauthorized
78,customerCode không được phân quyền,,Không hợp lệ,Cao,trả về lỗi
79,customerCode không tồn tại,,Không hợp lệ,Trung bình,trả về lỗi
,API getListContract,,,,
80,Gọi API không truyền tham số - Tài khoản khách hàng root,Không có,Hợp lệ,Cao,"200 OK, trả về trả về danh sách mặc định (trang đầu tiên) với dữ liệu danh sách hợp đồng theo quyền tài khoản root được cấp"
81,Gọi API không truyền tham số - Tài khoản khách hàng mức con,Không có,Hợp lệ,Cao,"201 OK, trả về trả về danh sách mặc định (trang đầu tiên) với dữ liệu danh sách hợp đồng theo quyền tài khoản mức con được cấp"
82,Lọc theo customerCode,customerCode=C12345,Hợp lệ,Cao,"200 OK, danh sách chứa customerCode tương ứng"
83,Lọc theo customerName,customerName=Nguyen Van A,Hợp lệ,Cao,"200 OK, danh sách chứa khách hàng có tên khớp"
84,Lọc theo contractCode,contractCode=CTR2024001,Hợp lệ,Cao,"200 OK, trả về hợp đồng có mã tương ứng"
85,Lọc theo centerCode,centerCode=CEN01,Hợp lệ,Trung bình,"200 OK, danh sách hợp đồng thuộc trung tâm CEN01"
86,Lọc theo paymentName,paymentName=Vietcombank,Hợp lệ,Trung bình,"200 OK, hợp đồng thanh toán qua Vietcombank"
87,Lọc theo contactPhone,contactPhone=**********,Hợp lệ,Trung bình,"200 OK, hợp đồng có số điện thoại tương ứng"
88,Lọc theo contractor,contractor=Tran Van C,Hợp lệ,Trung bình,"200 OK, hợp đồng có người đại diện là Tran Van C"
89,Phân trang,"page=1, size=10",Hợp lệ,Thấp,"200 OK, danh sách gồm 10 bản ghi tại trang 1"
90,Sắp xếp tăng dần theo tên,"sort=customerName,asc",Hợp lệ,Thấp,"200 OK, danh sách sắp theo tên A-Z"
91,API chưa được cấp quyền (cấu hình ko cấp quyền ở web),,Không hợp lệ,Cao,401 Unauthorized
92,sai token,,Không hợp lệ,Cao,402 Unauthorized
93,do dùng token cũ sau khi thực hiện API Register để generate token mới,,Không hợp lệ,Cao,402 Unauthorized
94,do dùng token cũ sau khi đổi Secret Key,,Không hợp lệ,Cao,402 Unauthorized
95,do cấp quyền API chuyển sang trạng thái Ngừng hoạt động,,Không hợp lệ,Cao,402 Unauthorized
96,do tài khoản khách hàng chuyển sang trạng thái Không hoạt động,,Không hợp lệ,Cao,402 Unauthorized
97,customerCode không tồn tại,,Không hợp lệ,Trung bình,trả về lỗi
98,customerCode không được phân quyền,,Không hợp lệ,Trung bình,trả về lỗi
99,customerName không tồn tại,,Không hợp lệ,Trung bình,trả về lỗi
100,contractCode không được phân quyền,,Không hợp lệ,Trung bình,trả về lỗi
101,contractCode không tồn tại,,Không hợp lệ,Trung bình,trả về lỗi
102,contractCode chứa ký tự đặc biệt,contractCode=CTR#2024,Không hợp lệ,Thấp,trả về lỗi
103,centerCode không tồn tại,centerCode=XYZ,Không hợp lệ,Thấp,trả về lỗi
104,paymentName sai định dạng,paymentName=@@@,Không hợp lệ,Thấp,trả về lỗi
105,contactPhone sai định dạng,contactPhone=abc123,Không hợp lệ,Thấp,trả về lỗi
106,sort sai định dạng,sort=contractCode-down,Không hợp lệ,Thấp,trả về lỗi
107,page âm,page=-1,Không hợp lệ,Thấp,trả về lỗi
108,size quá lớn,size=10000,Không hợp lệ,Thấp,trả về lỗi
,API getDetailContract,,,,
109,Gọi API với contractCode hợp lệ - Tài khoản khách hàng root,Không có,Hợp lệ,Cao,"200 OK, trả về với dữ liệu chi tiết hợp đồng theo quyền tài khoản root được cấp"
110,Gọi API với contractCode hợp lệ - Tài khoản khách hàng mức con,Không có,Hợp lệ,Cao,"200 OK, trả về với dữ liệu chi tiết hợp đồng theo quyền tài khoản mức con được cấp"
111,API chưa được cấp quyền (cấu hình ko cấp quyền ở web),,Không hợp lệ,Cao,401 Unauthorized
112,sai token,,Không hợp lệ,Cao,402 Unauthorized
113,do dùng token cũ sau khi thực hiện API Register để generate token mới,,Không hợp lệ,Cao,402 Unauthorized
114,do dùng token cũ sau khi đổi Secret Key,,Không hợp lệ,Cao,402 Unauthorized
115,do cấp quyền API chuyển sang trạng thái Ngừng hoạt động,,Không hợp lệ,Cao,402 Unauthorized
116,do tài khoản khách hàng chuyển sang trạng thái Không hoạt động,,Không hợp lệ,Cao,402 Unauthorized
117,contractCode không được phân quyền,,Không hợp lệ,Cao,trả về lỗi
118,contractCode không tồn tại,,Không hợp lệ,Trung bình,trả về lỗi
,API getListTrafficWallet,,,,
119,Gọi API không truyền tham số - Tài khoản khách hàng root,Không có,Hợp lệ,Cao,"200 OK, trả về trả về danh sách mặc định (trang đầu tiên) với dữ liệu danh sách ví theo quyền tài khoản root được cấp"
120,Gọi API không truyền tham số - Tài khoản khách hàng mức con,Không có,Hợp lệ,Cao,"201 OK, trả về trả về danh sách mặc định (trang đầu tiên) với dữ liệu danh sách ví theo quyền tài khoản mức con được cấp"
121,Tìm theo searchWalletCode,searchWalletCode=WALLET123,Hợp lệ,Cao,"200 OK, danh sách khớp mã ví"
122,Tìm theo searchPayCode,searchPayCode=PAY456,Hợp lệ,Cao,"200 OK, hợp đồng có mã thanh toán khớp"
123,Tìm theo searchName,searchName=Nguyen Van A,Hợp lệ,Cao,"200 OK, danh sách chứa tên khớp"
124,Tìm theo searchPhone,searchPhone=**********,Hợp lệ,Cao,"200 OK, hợp đồng chứa số điện thoại khớp"
125,Lọc theo khoảng thời gian startDate - endDate,"startDate=2024-01-01, endDate=2024-12-31",Hợp lệ,Trung bình,"200 OK, hợp đồng trong khoảng thời gian"
126,Lọc theo độ chính xác ngày accuracyStartDate,accuracyStartDate=2024-01-01,Hợp lệ,Trung bình,"200 OK, ngày bắt đầu chính xác"
127,Lọc theo packageName,packageName=Data50,Hợp lệ,Cao,"200 OK, hợp đồng theo gói Data50"
128,Lọc theo lstTrafficType,"lstTrafficType=[""internet""]",Hợp lệ,Trung bình,"200 OK, lọc đúng loại dịch vụ"
129,Lọc theo lstPackageName,"lstPackageName=[""Data50"", ""Data100""]",Hợp lệ,Trung bình,"200 OK, hợp đồng thuộc các gói tương ứng"
130,Lọc theo lstPaymentCode,"lstPaymentCode=[""PAY1"", ""PAY2""]",Hợp lệ,Trung bình,"200 OK, hợp đồng có mã thanh toán khớp"
131,Lọc theo paymentCode,paymentCode=PAY1,Hợp lệ,Trung bình,"200 OK, hợp đồng có mã thanh toán PAY1"
132,Lọc theo subCode,subCode=SUB123,Hợp lệ,Trung bình,"200 OK, hợp đồng theo mã thuê bao"
133,Lọc theo fromDate - toDate,"fromDate=2024-06-01, toDate=2024-06-30",Hợp lệ,Trung bình,"200 OK, hợp đồng trong khoảng tháng 6"
134,Lọc theo transId,transId=TX123456,Hợp lệ,Trung bình,"200 OK, giao dịch tương ứng"
135,Lọc theo autoType,autoType=1,Hợp lệ,Trung bình,"200 OK, hợp đồng có loại tự động tương ứng"
136,Lọc theo autoGroupId,autoGroupId=AG001,Hợp lệ,Trung bình,"200 OK, nhóm tự động AG001"
137,Tìm theo searchPackageName,searchPackageName=ComboNet,Hợp lệ,Trung bình,"200 OK, hợp đồng có gói chứa ComboNet"
138,"Phân trang với size, page","size=10, page=1",Hợp lệ,Thấp,"200 OK, trả về 10 hợp đồng của trang 1"
139,Sắp xếp tăng theo ngày,"sort=startDate,asc",Hợp lệ,Thấp,"200 OK, danh sách sắp theo ngày tăng"
140,API chưa được cấp quyền (cấu hình ko cấp quyền ở web),,Không hợp lệ,Cao,401 Unauthorized
141,sai token,,Không hợp lệ,Cao,402 Unauthorized
142,do dùng token cũ sau khi thực hiện API Register để generate token mới,,Không hợp lệ,Cao,402 Unauthorized
143,do dùng token cũ sau khi đổi Secret Key,,Không hợp lệ,Cao,402 Unauthorized
144,do cấp quyền API chuyển sang trạng thái Ngừng hoạt động,,Không hợp lệ,Cao,402 Unauthorized
145,do tài khoản khách hàng chuyển sang trạng thái Không hoạt động,,Không hợp lệ,Cao,402 Unauthorized
146,searchWalletCode không tồn tại,,Không hợp lệ,Trung bình,trả về lỗi
147,searchWalletCode không được phân quyền,,Không hợp lệ,Trung bình,trả về lỗi
148,searchPayCode không tồn tại,,Không hợp lệ,Trung bình,trả về lỗi
149,packageName không tồn tại,,Không hợp lệ,Trung bình,trả về lỗi
150,paymentCode không tồn tại,,Không hợp lệ,Trung bình,trả về lỗi
151,subCode không tồn tại,,Không hợp lệ,Trung bình,trả về lỗi
152,page âm,page=-1,Không hợp lệ,Thấp,trả về lỗi
153,size quá lớn,size=10000,Không hợp lệ,Thấp,trả về lỗi
154,sort sai định dạng,sort=startDate-down,Không hợp lệ,Thấp,trả về lỗi
155,searchPhone sai định dạng,searchPhone=abc123,Không hợp lệ,Thấp,trả về lỗi
156,startDate > endDate,"startDate=2024-12-01, endDate=2024-01-01",Không hợp lệ,Thấp,trả về lỗi
,API getDetailTrafficWallet,,,,
157,Gọi API với tất cả các tham số hợp lệ - Tài khoản khách hàng root,Không có,Hợp lệ,Cao,"200 OK, trả về với dữ liệu chi tiết ví theo quyền tài khoản root được cấp"
158,Gọi API với tất cả các tham số hợp lệ - Tài khoản khách hàng mức con,Không có,Hợp lệ,Cao,"200 OK, trả về với dữ liệu chi tiết ví theo quyền tài khoản mức con được cấp"
159,Gọi với chỉ paymentCode và subCode,,Hợp lệ,Cao,"200 OK, danh sách dữ liệu lưu lượng cho thuê bao"
160,Gọi với fromDate và toDate,,Hợp lệ,Cao,"200 OK, dữ liệu lưu lượng trong tháng 6"
161,Gọi chỉ với transId,,Hợp lệ,Cao,"200 OK, trả về chi tiết giao dịch tương ứng"
162,API chưa được cấp quyền (cấu hình ko cấp quyền ở web),,Không hợp lệ,Cao,401 Unauthorized
163,sai token,,Không hợp lệ,Cao,402 Unauthorized
164,do dùng token cũ sau khi thực hiện API Register để generate token mới,,Không hợp lệ,Cao,402 Unauthorized
165,do dùng token cũ sau khi đổi Secret Key,,Không hợp lệ,Cao,402 Unauthorized
166,do cấp quyền API chuyển sang trạng thái Ngừng hoạt động,,Không hợp lệ,Cao,402 Unauthorized
167,do tài khoản khách hàng chuyển sang trạng thái Không hoạt động,,Không hợp lệ,Cao,402 Unauthorized
168,paymentCode không tồn tại,,Không hợp lệ,Trung bình,trả về lỗi
169,subCode không tồn tại,,Không hợp lệ,Trung bình,trả về lỗi
170,transId không tồn tại,,Không hợp lệ,Trung bình,trả về lỗi
171,fromDate sai định dạng,,Không hợp lệ,Thấp,trả về lỗi
172,toDate sai định dạng,,Không hợp lệ,Thấp,trả về lỗi
,API getListShareWallet,,,,
173,Gọi API không truyền tham số (lấy toàn bộ danh sách mặc định) - Tài khoản khách hàng root,Không có,Hợp lệ,Cao,"200 OK, trả về danh sách mặc định (trang đầu tiên) với dữ liệu danh sách ví chia sẻ theo quyền tài khoản root được cấp"
174,Gọi API không truyền tham số (lấy toàn bộ danh sách mặc định) - Tài khoản khách hàng mức con,Không có,Hợp lệ,Cao,"201 OK, trả về danh sách mặc định (trang đầu tiên) với dữ liệu danh sách ví chia sẻ theo quyền tài khoản mức con được cấp"
175,Lọc theo subCode hợp lệ,,Hợp lệ,Cao,"200 OK, danh sách ví chia sẻ có subCode này"
176,Phân trang,"page=1, size=10",Hợp lệ,Trung bình,"200 OK, trả về 10 bản ghi ở trang số 1"
177,API chưa được cấp quyền (cấu hình ko cấp quyền ở web),,Không hợp lệ,Cao,401 Unauthorized
178,sai token,,Không hợp lệ,Cao,402 Unauthorized
179,do dùng token cũ sau khi thực hiện API Register để generate token mới,,Không hợp lệ,Cao,402 Unauthorized
180,do dùng token cũ sau khi đổi Secret Key,,Không hợp lệ,Cao,402 Unauthorized
181,do cấp quyền API chuyển sang trạng thái Ngừng hoạt động,,Không hợp lệ,Cao,402 Unauthorized
182,do tài khoản khách hàng chuyển sang trạng thái Không hoạt động,,Không hợp lệ,Cao,402 Unauthorized
183,subCode không tồn tại,,Không hợp lệ,Trung bình,trả về lỗi
184,page âm,page=-1,Không hợp lệ,Thấp,trả về lỗi
185,size quá lớn,size=1000,Không hợp lệ,Thấp,trả về lỗi
186,sort sai định dạng,sort=contractDate-down,Không hợp lệ,Thấp,trả về lỗi
,API getWalletHistory,,,,
,,,,,
,,,,,
,,,,,
,,,,,
,API shareTraffic,,,,
,,,,,
,,,,,
,,,,,
,,,,,
,,,,,
,API createDevice,,,,
,,,,,
,,,,,
,,,,,
,,,,,
,,,,,
,API updateDevice,,,,
