{"info": {"_postman_id": "m2m-api-additional-testcases", "name": "M2M API Additional Test Cases", "description": "Additional detailed test cases for remaining M2M APIs (Contract, Traffic Wallet, Share Wallet, etc.)", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "item": [{"name": "GetListContract - Valid Request", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test(\"Response contains contract list\", function () {", "    var jsonData = pm.response.json();", "    pm.expect(jsonData.errorCode).to.eql(0);", "    pm.expect(jsonData.errorDesc).to.eql(\"SUCCESS\");", "    pm.expect(jsonData.data).to.be.an('array');", "});"], "type": "text/javascript"}}], "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{auth_token}}", "type": "text"}], "url": {"raw": "{{base_url}}/api/msimapi/getListContract?page=0&size=10", "host": ["{{base_url}}"], "path": ["api", "m<PERSON><PERSON><PERSON>", "getListContract"], "query": [{"key": "page", "value": "0"}, {"key": "size", "value": "10"}]}}, "response": []}, {"name": "GetListContract - Filter by Contract Code", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test(\"Response contains filtered contracts\", function () {", "    var jsonData = pm.response.json();", "    pm.expect(jsonData.errorCode).to.eql(0);", "    pm.expect(jsonData.errorDesc).to.eql(\"SUCCESS\");", "});"], "type": "text/javascript"}}], "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{auth_token}}", "type": "text"}], "url": {"raw": "{{base_url}}/api/msimapi/getListContract?contractCode=HD001", "host": ["{{base_url}}"], "path": ["api", "m<PERSON><PERSON><PERSON>", "getListContract"], "query": [{"key": "contractCode", "value": "HD001"}]}}, "response": []}, {"name": "GetListContract - Filter by Date Range", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test(\"Response contains contracts within date range\", function () {", "    var jsonData = pm.response.json();", "    pm.expect(jsonData.errorCode).to.eql(0);", "    pm.expect(jsonData.errorDesc).to.eql(\"SUCCESS\");", "});"], "type": "text/javascript"}}], "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{auth_token}}", "type": "text"}], "url": {"raw": "{{base_url}}/api/msimapi/getListContract?fromDate=2024-01-01&toDate=2024-12-31", "host": ["{{base_url}}"], "path": ["api", "m<PERSON><PERSON><PERSON>", "getListContract"], "query": [{"key": "fromDate", "value": "2024-01-01"}, {"key": "toDate", "value": "2024-12-31"}]}}, "response": []}, {"name": "GetListContract - No Authorization", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 401\", function () {", "    pm.response.to.have.status(401);", "});", "", "pm.test(\"Response indicates unauthorized access\", function () {", "    pm.expect(pm.response.code).to.eql(401);", "});"], "type": "text/javascript"}}], "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/msimapi/getListContract", "host": ["{{base_url}}"], "path": ["api", "m<PERSON><PERSON><PERSON>", "getListContract"]}}, "response": []}, {"name": "GetListContract - Invalid Date Range", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Response indicates error for invalid date range\", function () {", "    var jsonData = pm.response.json();", "    pm.expect(jsonData.errorCode).to.not.eql(0);", "});"], "type": "text/javascript"}}], "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{auth_token}}", "type": "text"}], "url": {"raw": "{{base_url}}/api/msimapi/getListContract?fromDate=2024-12-31&toDate=2024-01-01", "host": ["{{base_url}}"], "path": ["api", "m<PERSON><PERSON><PERSON>", "getListContract"], "query": [{"key": "fromDate", "value": "2024-12-31"}, {"key": "toDate", "value": "2024-01-01"}]}}, "response": []}, {"name": "GetDetailContract - Valid Contract Code", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test(\"Response contains contract details\", function () {", "    var jsonData = pm.response.json();", "    pm.expect(jsonData.errorCode).to.eql(0);", "    pm.expect(jsonData.errorDesc).to.eql(\"SUCCESS\");", "    pm.expect(jsonData.data).to.be.an('object');", "});"], "type": "text/javascript"}}], "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{auth_token}}", "type": "text"}], "url": {"raw": "{{base_url}}/api/msimapi/getDetailContract?contractCode=HD001", "host": ["{{base_url}}"], "path": ["api", "m<PERSON><PERSON><PERSON>", "getDetailContract"], "query": [{"key": "contractCode", "value": "HD001"}]}}, "response": []}, {"name": "GetDetailContract - Non-existent Contract Code", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Response indicates error for non-existent contract\", function () {", "    var jsonData = pm.response.json();", "    pm.expect(jsonData.errorCode).to.not.eql(0);", "});"], "type": "text/javascript"}}], "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{auth_token}}", "type": "text"}], "url": {"raw": "{{base_url}}/api/msimapi/getDetailContract?contractCode=NONEXISTENT", "host": ["{{base_url}}"], "path": ["api", "m<PERSON><PERSON><PERSON>", "getDetailContract"], "query": [{"key": "contractCode", "value": "NONEXISTENT"}]}}, "response": []}, {"name": "GetListTrafficWallet - Valid Request", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test(\"Response contains traffic wallet list\", function () {", "    var jsonData = pm.response.json();", "    pm.expect(jsonData.errorCode).to.eql(0);", "    pm.expect(jsonData.errorDesc).to.eql(\"SUCCESS\");", "    pm.expect(jsonData.data).to.be.an('array');", "});"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{auth_token}}", "type": "text"}, {"key": "Content-Type", "value": "application/json", "type": "text"}], "body": {"mode": "raw", "raw": "{\n  \"trafficWalletReqDTO\": {\n    \"page\": 0,\n    \"size\": 10\n  }\n}"}, "url": {"raw": "{{base_url}}/api/msimapi/getListTrafficWallet", "host": ["{{base_url}}"], "path": ["api", "m<PERSON><PERSON><PERSON>", "getListTrafficWallet"]}}, "response": []}, {"name": "GetListTrafficWallet - Filter by Payment Code", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test(\"Response contains filtered traffic wallets\", function () {", "    var jsonData = pm.response.json();", "    pm.expect(jsonData.errorCode).to.eql(0);", "    pm.expect(jsonData.errorDesc).to.eql(\"SUCCESS\");", "});"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{auth_token}}", "type": "text"}, {"key": "Content-Type", "value": "application/json", "type": "text"}], "body": {"mode": "raw", "raw": "{\n  \"trafficWalletReqDTO\": {\n    \"paymentCode\": \"PAY001\",\n    \"page\": 0,\n    \"size\": 10\n  }\n}"}, "url": {"raw": "{{base_url}}/api/msimapi/getListTrafficWallet", "host": ["{{base_url}}"], "path": ["api", "m<PERSON><PERSON><PERSON>", "getListTrafficWallet"]}}, "response": []}, {"name": "GetListTrafficWallet - No Authorization", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 401\", function () {", "    pm.response.to.have.status(401);", "});", "", "pm.test(\"Response indicates unauthorized access\", function () {", "    pm.expect(pm.response.code).to.eql(401);", "});"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json", "type": "text"}], "body": {"mode": "raw", "raw": "{\n  \"trafficWalletReqDTO\": {\n    \"page\": 0,\n    \"size\": 10\n  }\n}"}, "url": {"raw": "{{base_url}}/api/msimapi/getListTrafficWallet", "host": ["{{base_url}}"], "path": ["api", "m<PERSON><PERSON><PERSON>", "getListTrafficWallet"]}}, "response": []}, {"name": "GetListTrafficWallet - Invalid Request Body", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Response indicates error for invalid request body\", function () {", "    var jsonData = pm.response.json();", "    pm.expect(jsonData.errorCode).to.not.eql(0);", "});"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{auth_token}}", "type": "text"}, {"key": "Content-Type", "value": "application/json", "type": "text"}], "body": {"mode": "raw", "raw": "{\n  \"invalidField\": \"invalidValue\"\n}"}, "url": {"raw": "{{base_url}}/api/msimapi/getListTrafficWallet", "host": ["{{base_url}}"], "path": ["api", "m<PERSON><PERSON><PERSON>", "getListTrafficWallet"]}}, "response": []}], "variable": [{"key": "base_url", "value": "https://api-m2m.vinaphone.com.vn", "type": "string"}, {"key": "auth_token", "value": "your_bearer_token_here", "type": "string"}]}