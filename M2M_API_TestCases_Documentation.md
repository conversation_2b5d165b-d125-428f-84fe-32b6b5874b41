# M2M API Postman Collection - Comprehensive Test Cases Documentation

## Tổng quan

Tôi đã tạo ra một bộ collection Postman hoàn chỉnh cho tất cả các API M2M với các test cases chi tiết theo mẫu của GetListDevice API. Collection bao gồm:

## Files được tạo

1. **GetListDevice_Postman_Collection.json** - Collection chính chứa:
   - Tất cả test cases cho GetListDevice (TC06-TC31)
   - Test cases chi tiết cho các API #7-#11 (Device và Customer APIs)

2. **M2M_API_Additional_TestCases.json** - Collection bổ sung chứa:
   - Test cases chi tiết cho các API #12-#19 (Contract, Traffic Wallet, Share Wallet APIs)

## Chi tiết Test Cases theo từng API

### API #6: GetListDevice (TC06-TC31)
**Valid Test Cases:**
- TC06: Filter by country (VN)
- TC07: Filter by device type (smart_meter)
- TC08: Filter by contract date range
- TC09: Multiple filters combined
- TC10: Pagination (page=1, size=10)
- TC11: Sort ascending by contract date
- TC12: Sort descending by contract date

**Authentication/Authorization Test Cases:**
- TC13: No authorization header (expects 401)
- TC14: Invalid token (expects 402)
- TC15: Old token after register (expects 402)
- TC16: Old token after secret key change (expects 402)
- TC17: Deactivated API permission (expects 402)
- TC18: Inactive customer account (expects 402)

**Invalid Input Test Cases:**
- TC19-TC31: Various invalid inputs (msisdn, imei, location, country, deviceType, dates, pagination, sort)

### API #7: GetDetailDevice
**Test Cases:**
- Valid MSISDN request
- No authorization (401)
- Invalid token (402)
- Non-existent MSISDN
- Invalid MSISDN format

### API #8: CreateDevice
**Test Cases:**
- Valid device creation
- No authorization (401)
- Invalid token (402)
- Invalid MSISDN format
- Invalid IMEI length
- Missing required fields

### API #9: UpdateDevice
**Test Cases:**
- Valid device update
- No authorization (401)
- Invalid token (402)
- Non-existent device
- Invalid data formats

### API #10: GetListCustomer
**Test Cases:**
- Default customer list
- Filter by customer code
- Filter by customer name
- Multiple filters combined
- No authorization (401)
- Invalid pagination parameters

### API #11: GetDetailCustomer
**Test Cases:**
- Valid customer ID
- No authorization (401)
- Invalid token (402)
- Non-existent customer ID
- Invalid ID format

### API #12: GetListContract
**Test Cases:**
- Valid contract list request
- Filter by contract code
- Filter by date range
- No authorization (401)
- Invalid date range

### API #13: GetDetailContract
**Test Cases:**
- Valid contract code
- Non-existent contract code
- No authorization
- Invalid token

### API #14: GetListTrafficWallet
**Test Cases:**
- Valid request with pagination
- Filter by payment code
- No authorization (401)
- Invalid request body format

### API #15: GetDetailTrafficWallet
**Test Cases:**
- Valid payment code with date range
- No authorization
- Invalid parameters

### API #16: GetListShareWallet
**Test Cases:**
- Valid request with subCode
- Pagination parameters
- No authorization
- Invalid subCode

### API #17: GetWalletHistory
**Test Cases:**
- Valid wallet history request
- Pagination parameters
- No authorization
- Invalid date range

### API #18: ShareTraffic
**Test Cases:**
- Valid traffic sharing
- No authorization
- Invalid MSISDN
- Insufficient traffic balance
- Invalid request body

### API #19: GetTerminalUsageDataDetails
**Test Cases:**
- Valid usage data request
- Filter by date range
- No authorization
- Invalid MSISDN
- Invalid date format

## Cấu trúc Test Cases

Mỗi API đều có các loại test cases sau:

### 1. Valid Test Cases
- Các request hợp lệ với tham số đúng
- Kiểm tra response status 200
- Kiểm tra errorCode = 0 và errorDesc = "SUCCESS"
- Kiểm tra cấu trúc data response

### 2. Authentication/Authorization Test Cases
- No Authorization header (expects 401)
- Invalid/expired token (expects 402)
- Deactivated permissions (expects 402)

### 3. Invalid Input Test Cases
- Invalid parameter formats
- Non-existent resources
- Out-of-range values
- Missing required fields
- Invalid date ranges

## Environment Variables

```json
{
  "base_url": "https://api-m2m.vinaphone.com.vn",
  "auth_token": "your_bearer_token_here",
  "old_token_after_register": "old_token_after_register_here",
  "old_token_after_secret_change": "old_token_after_secret_change_here",
  "deactivated_api_token": "deactivated_api_token_here",
  "inactive_customer_token": "inactive_customer_token_here"
}
```

## Cách sử dụng

1. **Import Collections:**
   - Import `GetListDevice_Postman_Collection.json` vào Postman
   - Import `M2M_API_Additional_TestCases.json` vào Postman

2. **Setup Environment:**
   - Tạo environment mới trong Postman
   - Thêm các variables như trên
   - Cập nhật giá trị token thực tế

3. **Run Tests:**
   - Chạy từng request riêng lẻ
   - Hoặc chạy toàn bộ collection
   - Kiểm tra test results trong Test Results tab

## Lưu ý quan trọng

- Tất cả test cases đều được thiết kế theo mẫu của GetListDevice
- Mỗi request đều có test scripts để validate response
- Environment variables cần được cập nhật với giá trị thực tế
- Một số test cases có thể cần điều chỉnh tham số theo dữ liệu thực tế

## Kết quả mong đợi

Sau khi chạy test, bạn sẽ có:
- Báo cáo chi tiết về tình trạng của từng API
- Xác định các lỗi authentication/authorization
- Kiểm tra validation của input parameters
- Đánh giá tính ổn định của API endpoints
