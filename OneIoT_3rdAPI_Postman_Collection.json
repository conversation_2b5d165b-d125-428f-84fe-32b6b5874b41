{"info": {"_postman_id": "oneiot-3rdapi-collection", "name": "OneIoT 3rd Party API Collection", "description": "Collection for OneIoT 3rd Party APIs based on Test List_3rdAPI.csv", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{<PERSON>T<PERSON>}}", "type": "string"}]}, "variable": [{"key": "baseURL", "value": "https://api-m2m.oneiot.com.vn", "type": "string"}, {"key": "bearerToken", "value": "eyJhbGciOiJIUzI1NiJ9.***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.cGRp12kIQXh-ahXybsyfDO1xfXQZwLJptAQ0TwxYqoY", "type": "string"}], "item": [{"name": "Device APIs", "item": [{"name": "getListDevice - No Parameters (Root Account)", "request": {"method": "GET", "header": [{"key": "Content-Type", "value": "application/json"}], "url": {"raw": "{{baseURL}}/api/msimapi/getListDevice", "host": ["{{baseURL}}"], "path": ["api", "m<PERSON><PERSON><PERSON>", "getListDevice"]}, "description": "Gọi API không truyền tham số (lấy toàn bộ danh sách mặc định) - Tài khoản khách hàng root"}}, {"name": "getListDevice - Filter by MSISDN", "request": {"method": "GET", "header": [{"key": "Content-Type", "value": "application/json"}], "url": {"raw": "{{baseURL}}/api/msimapi/getListDevice?msisdn=**********", "host": ["{{baseURL}}"], "path": ["api", "m<PERSON><PERSON><PERSON>", "getListDevice"], "query": [{"key": "msisdn", "value": "**********"}]}, "description": "<PERSON><PERSON><PERSON> theo msisdn hợp lệ"}}, {"name": "getListDevice - Filter by IMEI", "request": {"method": "GET", "header": [{"key": "Content-Type", "value": "application/json"}], "url": {"raw": "{{baseURL}}/api/msimapi/getListDevice?imei=***************", "host": ["{{baseURL}}"], "path": ["api", "m<PERSON><PERSON><PERSON>", "getListDevice"], "query": [{"key": "imei", "value": "***************"}]}, "description": "<PERSON><PERSON><PERSON> theo im<PERSON> h<PERSON> l<PERSON>"}}, {"name": "getDetailDevice - <PERSON><PERSON> (Root Account)", "request": {"method": "GET", "header": [{"key": "Content-Type", "value": "application/json"}], "url": {"raw": "{{baseURL}}/api/msimapi/getDetailDevice?msisdn=**********", "host": ["{{baseURL}}"], "path": ["api", "m<PERSON><PERSON><PERSON>", "getDetailDevice"], "query": [{"key": "msisdn", "value": "**********"}]}, "description": "Gọi API với msisdn hợp lệ - <PERSON><PERSON>i k<PERSON>n khách hàng root"}}, {"name": "createDevice", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"msisdn\": \"***********\",\n    \"imei\": \"***************\",\n    \"country\": \"Vietnam\",\n    \"deviceType\": \"IoT Device\",\n    \"iotLink\": 1,\n    \"expiredDate\": **********\n}"}, "url": {"raw": "{{baseURL}}/api/msimapi/createDevice", "host": ["{{baseURL}}"], "path": ["api", "m<PERSON><PERSON><PERSON>", "createDevice"]}, "description": "Create a new device"}}, {"name": "updateDevice", "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"msisdn\": \"***********\",\n    \"imei\": \"***************\",\n    \"country\": \"Vietnam\",\n    \"deviceType\": \"IoT Device Updated\",\n    \"iotLink\": 1,\n    \"expiredDate\": **********\n}"}, "url": {"raw": "{{baseURL}}/api/msimapi/updateDevice", "host": ["{{baseURL}}"], "path": ["api", "m<PERSON><PERSON><PERSON>", "updateDevice"]}, "description": "Update an existing device"}}]}, {"name": "Customer APIs", "item": [{"name": "getListCustomer - No Parameters (Root Account)", "request": {"method": "GET", "header": [{"key": "Content-Type", "value": "application/json"}], "url": {"raw": "{{baseURL}}/api/msimapi/getListCustomer", "host": ["{{baseURL}}"], "path": ["api", "m<PERSON><PERSON><PERSON>", "getListCustomer"]}, "description": "Gọi API không truyền tham số - Tài khoản khách hàng root"}}, {"name": "getListCustomer - Filter by Customer Code", "request": {"method": "GET", "header": [{"key": "Content-Type", "value": "application/json"}], "url": {"raw": "{{baseURL}}/api/msimapi/getListCustomer?customerCode=C12345", "host": ["{{baseURL}}"], "path": ["api", "m<PERSON><PERSON><PERSON>", "getListCustomer"], "query": [{"key": "customerCode", "value": "C12345"}]}, "description": "Lọc theo customerCode"}}, {"name": "getListCustomer - Filter by Customer Name", "request": {"method": "GET", "header": [{"key": "Content-Type", "value": "application/json"}], "url": {"raw": "{{baseURL}}/api/msimapi/getListCustomer?customerName=<PERSON><PERSON><PERSON>", "host": ["{{baseURL}}"], "path": ["api", "m<PERSON><PERSON><PERSON>", "getListCustomer"], "query": [{"key": "customerName", "value": "<PERSON><PERSON><PERSON>"}]}, "description": "Lọc theo customerName"}}, {"name": "getDetailCustomer - Valid Customer Code (Root Account)", "request": {"method": "GET", "header": [{"key": "Content-Type", "value": "application/json"}], "url": {"raw": "{{baseURL}}/api/msimapi/getDetailCustomer?customerCode=C12345", "host": ["{{baseURL}}"], "path": ["api", "m<PERSON><PERSON><PERSON>", "getDetailCustomer"], "query": [{"key": "customerCode", "value": "C12345"}]}, "description": "Gọi API với customerCode hợp lệ - T<PERSON>i kho<PERSON>n khách hàng root"}}]}, {"name": "Contract APIs", "item": [{"name": "getListContract - No Parameters (Root Account)", "request": {"method": "GET", "header": [{"key": "Content-Type", "value": "application/json"}], "url": {"raw": "{{baseURL}}/api/msimapi/getListContract", "host": ["{{baseURL}}"], "path": ["api", "m<PERSON><PERSON><PERSON>", "getListContract"]}, "description": "Gọi API không truyền tham số - Tài khoản khách hàng root"}}, {"name": "getListContract - Filter by Customer Code", "request": {"method": "GET", "header": [{"key": "Content-Type", "value": "application/json"}], "url": {"raw": "{{baseURL}}/api/msimapi/getListContract?customerCode=C12345", "host": ["{{baseURL}}"], "path": ["api", "m<PERSON><PERSON><PERSON>", "getListContract"], "query": [{"key": "customerCode", "value": "C12345"}]}, "description": "Lọc theo customerCode"}}, {"name": "getListContract - Filter by Customer Name", "request": {"method": "GET", "header": [{"key": "Content-Type", "value": "application/json"}], "url": {"raw": "{{baseURL}}/api/msimapi/getListContract?customerName=<PERSON><PERSON><PERSON>", "host": ["{{baseURL}}"], "path": ["api", "m<PERSON><PERSON><PERSON>", "getListContract"], "query": [{"key": "customerName", "value": "<PERSON><PERSON><PERSON>"}]}, "description": "Lọc theo customerName"}}, {"name": "getListContract - Filter by Contract Code", "request": {"method": "GET", "header": [{"key": "Content-Type", "value": "application/json"}], "url": {"raw": "{{baseURL}}/api/msimapi/getListContract?contractCode=CTR2024001", "host": ["{{baseURL}}"], "path": ["api", "m<PERSON><PERSON><PERSON>", "getListContract"], "query": [{"key": "contractCode", "value": "CTR2024001"}]}, "description": "Lọc theo contractCode"}}, {"name": "getDetailContract - Valid Contract Code (Root Account)", "request": {"method": "GET", "header": [{"key": "Content-Type", "value": "application/json"}], "url": {"raw": "{{baseURL}}/api/msimapi/getDetailContract?contractCode=CTR2024001", "host": ["{{baseURL}}"], "path": ["api", "m<PERSON><PERSON><PERSON>", "getDetailContract"], "query": [{"key": "contractCode", "value": "CTR2024001"}]}, "description": "Gọi API với contractCode hợp lệ - <PERSON><PERSON>i k<PERSON>n khách hàng root"}}]}, {"name": "Traffic Wallet APIs", "item": [{"name": "getListTrafficWallet - No Parameters (Root Account)", "request": {"method": "GET", "header": [{"key": "Content-Type", "value": "application/json"}], "url": {"raw": "{{baseURL}}/api/msimapi/getListTrafficWallet", "host": ["{{baseURL}}"], "path": ["api", "m<PERSON><PERSON><PERSON>", "getListTrafficWallet"]}, "description": "Gọi API không truyền tham số - Tài khoản khách hàng root"}}, {"name": "getListTrafficWallet - Search by Wallet Code", "request": {"method": "GET", "header": [{"key": "Content-Type", "value": "application/json"}], "url": {"raw": "{{baseURL}}/api/msimapi/getListTrafficWallet?searchWalletCode=WALLET123", "host": ["{{baseURL}}"], "path": ["api", "m<PERSON><PERSON><PERSON>", "getListTrafficWallet"], "query": [{"key": "searchWalletCode", "value": "WALLET123"}]}, "description": "Tìm theo searchWalletCode"}}, {"name": "getListTrafficWallet - Search by Pay Code", "request": {"method": "GET", "header": [{"key": "Content-Type", "value": "application/json"}], "url": {"raw": "{{baseURL}}/api/msimapi/getListTrafficWallet?searchPayCode=PAY456", "host": ["{{baseURL}}"], "path": ["api", "m<PERSON><PERSON><PERSON>", "getListTrafficWallet"], "query": [{"key": "searchPayCode", "value": "PAY456"}]}, "description": "Tìm theo searchPayCode"}}, {"name": "getListTrafficWallet - Search by Name", "request": {"method": "GET", "header": [{"key": "Content-Type", "value": "application/json"}], "url": {"raw": "{{baseURL}}/api/msimapi/getListTrafficWallet?searchName=<PERSON><PERSON><PERSON>", "host": ["{{baseURL}}"], "path": ["api", "m<PERSON><PERSON><PERSON>", "getListTrafficWallet"], "query": [{"key": "searchName", "value": "<PERSON><PERSON><PERSON>"}]}, "description": "<PERSON><PERSON><PERSON> theo search<PERSON>"}}, {"name": "getListTrafficWallet - Search by Phone", "request": {"method": "GET", "header": [{"key": "Content-Type", "value": "application/json"}], "url": {"raw": "{{baseURL}}/api/msimapi/getListTrafficWallet?searchPhone=0909123456", "host": ["{{baseURL}}"], "path": ["api", "m<PERSON><PERSON><PERSON>", "getListTrafficWallet"], "query": [{"key": "searchPhone", "value": "0909123456"}]}, "description": "<PERSON><PERSON><PERSON> theo searchPhone"}}, {"name": "getListTrafficWallet - Filter by Package Name", "request": {"method": "GET", "header": [{"key": "Content-Type", "value": "application/json"}], "url": {"raw": "{{baseURL}}/api/msimapi/getListTrafficWallet?packageName=Data50", "host": ["{{baseURL}}"], "path": ["api", "m<PERSON><PERSON><PERSON>", "getListTrafficWallet"], "query": [{"key": "packageName", "value": "Data50"}]}, "description": "Lọc theo packageName"}}, {"name": "getDetailTrafficWallet - All Parameters (Root Account)", "request": {"method": "GET", "header": [{"key": "Content-Type", "value": "application/json"}], "url": {"raw": "{{baseURL}}/api/msimapi/getDetailTrafficWallet?paymentCode=PAY123&subCode=SUB456", "host": ["{{baseURL}}"], "path": ["api", "m<PERSON><PERSON><PERSON>", "getDetailTrafficWallet"], "query": [{"key": "paymentCode", "value": "PAY123"}, {"key": "subCode", "value": "SUB456"}]}, "description": "Gọi API với tất cả các tham số hợp lệ - Tài kho<PERSON>n khách hàng root"}}, {"name": "getDetailTrafficWallet - With Date Range", "request": {"method": "GET", "header": [{"key": "Content-Type", "value": "application/json"}], "url": {"raw": "{{baseURL}}/api/msimapi/getDetailTrafficWallet?fromDate=2024-06-01&toDate=2024-06-30", "host": ["{{baseURL}}"], "path": ["api", "m<PERSON><PERSON><PERSON>", "getDetailTrafficWallet"], "query": [{"key": "fromDate", "value": "2024-06-01"}, {"key": "toDate", "value": "2024-06-30"}]}, "description": "Gọi với fromDate và toDate"}}, {"name": "getDetailTrafficWallet - By Transaction ID", "request": {"method": "GET", "header": [{"key": "Content-Type", "value": "application/json"}], "url": {"raw": "{{baseURL}}/api/msimapi/getDetailTrafficWallet?transId=TRANS123456", "host": ["{{baseURL}}"], "path": ["api", "m<PERSON><PERSON><PERSON>", "getDetailTrafficWallet"], "query": [{"key": "transId", "value": "TRANS123456"}]}, "description": "Gọi chỉ với transId"}}]}, {"name": "Share Wallet APIs", "item": [{"name": "getListShareWallet - No Parameters (Root Account)", "request": {"method": "GET", "header": [{"key": "Content-Type", "value": "application/json"}], "url": {"raw": "{{baseURL}}/api/msimapi/getListShareWallet", "host": ["{{baseURL}}"], "path": ["api", "m<PERSON><PERSON><PERSON>", "getListShareWallet"]}, "description": "Gọi API không truyền tham số (lấy toàn bộ danh sách mặc định) - Tài khoản khách hàng root"}}, {"name": "getListShareWallet - Filter by SubCode", "request": {"method": "GET", "header": [{"key": "Content-Type", "value": "application/json"}], "url": {"raw": "{{baseURL}}/api/msimapi/getListShareWallet?subCode=SUB123", "host": ["{{baseURL}}"], "path": ["api", "m<PERSON><PERSON><PERSON>", "getListShareWallet"], "query": [{"key": "subCode", "value": "SUB123"}]}, "description": "<PERSON><PERSON><PERSON> theo subCode hợp lệ"}}, {"name": "getWalletHistory", "request": {"method": "GET", "header": [{"key": "Content-Type", "value": "application/json"}], "url": {"raw": "{{baseURL}}/api/msimapi/getWalletHistory?walletId=WALLET123&fromDate=2024-01-01&toDate=2024-12-31", "host": ["{{baseURL}}"], "path": ["api", "m<PERSON><PERSON><PERSON>", "getWalletHistory"], "query": [{"key": "walletId", "value": "WALLET123"}, {"key": "fromDate", "value": "2024-01-01"}, {"key": "toDate", "value": "2024-12-31"}]}, "description": "Get wallet history with date range"}}, {"name": "shareTraffic", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"fromWalletId\": \"WALLET123\",\n    \"toWalletId\": \"WALLET456\",\n    \"amount\": 1000,\n    \"description\": \"Share traffic between wallets\"\n}"}, "url": {"raw": "{{baseURL}}/api/msimapi/shareTraffic", "host": ["{{baseURL}}"], "path": ["api", "m<PERSON><PERSON><PERSON>", "shareTraffic"]}, "description": "Share traffic between wallets"}}]}, {"name": "Error Test Cases", "item": [{"name": "Unauthorized - No Permission", "request": {"auth": {"type": "<PERSON><PERSON><PERSON>"}, "method": "GET", "header": [{"key": "Content-Type", "value": "application/json"}], "url": {"raw": "{{baseURL}}/api/msimapi/getListDevice", "host": ["{{baseURL}}"], "path": ["api", "m<PERSON><PERSON><PERSON>", "getListDevice"]}, "description": "API chưa đ<PERSON><PERSON><PERSON> cấp quyền (cấu hình ko cấp quyền ở web) - Expected: 401 Unauthorized"}}, {"name": "Invalid <PERSON>", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "invalid_token_here", "type": "string"}]}, "method": "GET", "header": [{"key": "Content-Type", "value": "application/json"}], "url": {"raw": "{{baseURL}}/api/msimapi/getListDevice", "host": ["{{baseURL}}"], "path": ["api", "m<PERSON><PERSON><PERSON>", "getListDevice"]}, "description": "sai token - Expected: 402 Unauthorized"}}]}]}