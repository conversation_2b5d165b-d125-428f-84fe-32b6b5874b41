{"info": {"_postman_id": "oneiot-3rdapi-complete-collection", "name": "OneIoT 3rd Party API Complete Collection", "description": "Complete collection for OneIoT 3rd Party APIs based on Test List_3rdAPI3.csv (67 high priority test cases)", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{<PERSON>T<PERSON>}}", "type": "string"}]}, "variable": [{"key": "baseURL", "value": "https://api-m2m.oneiot.com.vn", "type": "string"}, {"key": "bearerToken", "value": "eyJhbGciOiJIUzI1NiJ9.***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.cGRp12kIQXh-ahXybsyfDO1xfXQZwLJptAQ0TwxYqoY", "type": "string"}], "item": [{"name": "Device APIs", "item": [{"name": "getListDevice - No Parameters (Root Account)", "request": {"method": "GET", "header": [{"key": "Content-Type", "value": "application/json"}], "url": {"raw": "{{baseURL}}/api/msimapi/getListDevice", "host": ["{{baseURL}}"], "path": ["api", "m<PERSON><PERSON><PERSON>", "getListDevice"]}, "description": "Test Case 1: <PERSON><PERSON>i API không truyền tham số (l<PERSON>y toàn bộ danh sách mặc định) - T<PERSON><PERSON> khoản khách hàng root"}}, {"name": "getListDevice - No Parameters (Sub Account)", "request": {"method": "GET", "header": [{"key": "Content-Type", "value": "application/json"}], "url": {"raw": "{{baseURL}}/api/msimapi/getListDevice", "host": ["{{baseURL}}"], "path": ["api", "m<PERSON><PERSON><PERSON>", "getListDevice"]}, "description": "Test Case 2: <PERSON><PERSON><PERSON> không truyền tham số (l<PERSON><PERSON> toàn bộ danh sách mặc định) - <PERSON><PERSON><PERSON> kho<PERSON>n khách hàng mức con"}}, {"name": "getListDevice - Filter by MSISDN", "request": {"method": "GET", "header": [{"key": "Content-Type", "value": "application/json"}], "url": {"raw": "{{baseURL}}/api/msimapi/getListDevice?msisdn=**********", "host": ["{{baseURL}}"], "path": ["api", "m<PERSON><PERSON><PERSON>", "getListDevice"], "query": [{"key": "msisdn", "value": "**********"}]}, "description": "Test Case 3: <PERSON><PERSON><PERSON> theo msisdn hợp lệ"}}, {"name": "getListDevice - Filter by IMEI", "request": {"method": "GET", "header": [{"key": "Content-Type", "value": "application/json"}], "url": {"raw": "{{baseURL}}/api/msimapi/getListDevice?imei=123456789012345", "host": ["{{baseURL}}"], "path": ["api", "m<PERSON><PERSON><PERSON>", "getListDevice"], "query": [{"key": "imei", "value": "123456789012345"}]}, "description": "Test Case 4: <PERSON><PERSON><PERSON> theo imei h<PERSON> l<PERSON>"}}, {"name": "getListDevice - API Not Authorized", "request": {"auth": {"type": "<PERSON><PERSON><PERSON>"}, "method": "GET", "header": [{"key": "Content-Type", "value": "application/json"}], "url": {"raw": "{{baseURL}}/api/msimapi/getListDevice", "host": ["{{baseURL}}"], "path": ["api", "m<PERSON><PERSON><PERSON>", "getListDevice"]}, "description": "Test Case 13: <PERSON> chưa đ<PERSON><PERSON> cấp quyền (cấ<PERSON> hình ko cấp quyền ở web) - Expected: 401 Unauthorized"}}, {"name": "getListDevice - Invalid <PERSON>", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "invalid_token_here", "type": "string"}]}, "method": "GET", "header": [{"key": "Content-Type", "value": "application/json"}], "url": {"raw": "{{baseURL}}/api/msimapi/getListDevice", "host": ["{{baseURL}}"], "path": ["api", "m<PERSON><PERSON><PERSON>", "getListDevice"]}, "description": "Test Case 14: sai token - Expected: 402 Unauthorized"}}, {"name": "getListDevice - Old Token After Register", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "old_token_after_register", "type": "string"}]}, "method": "GET", "header": [{"key": "Content-Type", "value": "application/json"}], "url": {"raw": "{{baseURL}}/api/msimapi/getListDevice", "host": ["{{baseURL}}"], "path": ["api", "m<PERSON><PERSON><PERSON>", "getListDevice"]}, "description": "Test Case 15: do dùng token cũ sau khi thực hiện API Register để generate token mới - Expected: 402 Unauthorized"}}, {"name": "getListDevice - Old <PERSON><PERSON> After Secret Key Change", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "old_token_after_secret_change", "type": "string"}]}, "method": "GET", "header": [{"key": "Content-Type", "value": "application/json"}], "url": {"raw": "{{baseURL}}/api/msimapi/getListDevice", "host": ["{{baseURL}}"], "path": ["api", "m<PERSON><PERSON><PERSON>", "getListDevice"]}, "description": "Test Case 16: do dùng token cũ sau khi đổi Secret Key - Expected: 402 Unauthorized"}}, {"name": "getListDevice - API Permission Disabled", "request": {"method": "GET", "header": [{"key": "Content-Type", "value": "application/json"}], "url": {"raw": "{{baseURL}}/api/msimapi/getListDevice", "host": ["{{baseURL}}"], "path": ["api", "m<PERSON><PERSON><PERSON>", "getListDevice"]}, "description": "Test Case 17: do c<PERSON><PERSON> quyền API chuy<PERSON><PERSON> sang trạng thái <PERSON>ng hoạt động - Expected: 402 Unauthorized"}}, {"name": "getListDevice - Account Inactive", "request": {"method": "GET", "header": [{"key": "Content-Type", "value": "application/json"}], "url": {"raw": "{{baseURL}}/api/msimapi/getListDevice", "host": ["{{baseURL}}"], "path": ["api", "m<PERSON><PERSON><PERSON>", "getListDevice"]}, "description": "Test Case 18: do tà<PERSON> k<PERSON>n khách hàng chuyển sang trạng thái Không hoạt động - Expected: 402 Unauthorized"}}, {"name": "getListDevice - MSISDN Not Authorized", "request": {"method": "GET", "header": [{"key": "Content-Type", "value": "application/json"}], "url": {"raw": "{{baseURL}}/api/msimapi/getListDevice?msisdn=**********", "host": ["{{baseURL}}"], "path": ["api", "m<PERSON><PERSON><PERSON>", "getListDevice"], "query": [{"key": "msisdn", "value": "**********"}]}, "description": "Test Case 19: m<PERSON>dn không đ<PERSON><PERSON><PERSON> phân quyền - Expected: <PERSON><PERSON><PERSON>"}}]}, {"name": "getDetailDevice APIs", "item": [{"name": "getDetailDevice - <PERSON><PERSON> (Root Account)", "request": {"method": "GET", "header": [{"key": "Content-Type", "value": "application/json"}], "url": {"raw": "{{baseURL}}/api/msimapi/getDetailDevice?msisdn=**********", "host": ["{{baseURL}}"], "path": ["api", "m<PERSON><PERSON><PERSON>", "getDetailDevice"], "query": [{"key": "msisdn", "value": "**********"}]}, "description": "Test Case 32: Gọi API với msisdn hợp lệ - <PERSON><PERSON><PERSON> k<PERSON> kh<PERSON>ch hàng root"}}, {"name": "getDetailDevice - Valid MSISDN (Sub Account)", "request": {"method": "GET", "header": [{"key": "Content-Type", "value": "application/json"}], "url": {"raw": "{{baseURL}}/api/msimapi/getDetailDevice?msisdn=**********", "host": ["{{baseURL}}"], "path": ["api", "m<PERSON><PERSON><PERSON>", "getDetailDevice"], "query": [{"key": "msisdn", "value": "**********"}]}, "description": "Test Case 33: Gọi API với msisdn hợp lệ - <PERSON><PERSON><PERSON> k<PERSON> kh<PERSON>ch hàng mức con"}}, {"name": "getDetailDevice - API Not Authorized", "request": {"auth": {"type": "<PERSON><PERSON><PERSON>"}, "method": "GET", "header": [{"key": "Content-Type", "value": "application/json"}], "url": {"raw": "{{baseURL}}/api/msimapi/getDetailDevice?msisdn=**********", "host": ["{{baseURL}}"], "path": ["api", "m<PERSON><PERSON><PERSON>", "getDetailDevice"], "query": [{"key": "msisdn", "value": "**********"}]}, "description": "Test Case 34: <PERSON> chưa đ<PERSON><PERSON> cấp quyền (cấ<PERSON> hình ko cấp quyền ở web) - Expected: 401 Unauthorized"}}, {"name": "getDetailDevice - Invalid <PERSON>", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "invalid_token_here", "type": "string"}]}, "method": "GET", "header": [{"key": "Content-Type", "value": "application/json"}], "url": {"raw": "{{baseURL}}/api/msimapi/getDetailDevice?msisdn=**********", "host": ["{{baseURL}}"], "path": ["api", "m<PERSON><PERSON><PERSON>", "getDetailDevice"], "query": [{"key": "msisdn", "value": "**********"}]}, "description": "Test Case 35: sai token - Expected: 402 Unauthorized"}}, {"name": "getDetailDevice - Old Token After Register", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "old_token_after_register", "type": "string"}]}, "method": "GET", "header": [{"key": "Content-Type", "value": "application/json"}], "url": {"raw": "{{baseURL}}/api/msimapi/getDetailDevice?msisdn=**********", "host": ["{{baseURL}}"], "path": ["api", "m<PERSON><PERSON><PERSON>", "getDetailDevice"], "query": [{"key": "msisdn", "value": "**********"}]}, "description": "Test Case 36: do dùng token cũ sau khi thực hiện API Register để generate token mới - Expected: 402 Unauthorized"}}, {"name": "getDetailDevice - Old Token After Secret Key Change", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "old_token_after_secret_change", "type": "string"}]}, "method": "GET", "header": [{"key": "Content-Type", "value": "application/json"}], "url": {"raw": "{{baseURL}}/api/msimapi/getDetailDevice?msisdn=**********", "host": ["{{baseURL}}"], "path": ["api", "m<PERSON><PERSON><PERSON>", "getDetailDevice"], "query": [{"key": "msisdn", "value": "**********"}]}, "description": "Test Case 37: do dùng token cũ sau khi đổi Secret Key - Expected: 402 Unauthorized"}}, {"name": "getDetailDevice - API Permission Disabled", "request": {"method": "GET", "header": [{"key": "Content-Type", "value": "application/json"}], "url": {"raw": "{{baseURL}}/api/msimapi/getDetailDevice?msisdn=**********", "host": ["{{baseURL}}"], "path": ["api", "m<PERSON><PERSON><PERSON>", "getDetailDevice"], "query": [{"key": "msisdn", "value": "**********"}]}, "description": "Test Case 38: do c<PERSON><PERSON> quyền API chuy<PERSON><PERSON> sang trạng thái <PERSON>ng hoạt động - Expected: 402 Unauthorized"}}, {"name": "getDetailDevice - Account Inactive", "request": {"method": "GET", "header": [{"key": "Content-Type", "value": "application/json"}], "url": {"raw": "{{baseURL}}/api/msimapi/getDetailDevice?msisdn=**********", "host": ["{{baseURL}}"], "path": ["api", "m<PERSON><PERSON><PERSON>", "getDetailDevice"], "query": [{"key": "msisdn", "value": "**********"}]}, "description": "Test Case 39: do tà<PERSON> k<PERSON>n khách hàng chuyể<PERSON> sang trạng thái Không hoạt động - Expected: 402 Unauthorized"}}, {"name": "getDetailDevice - MSISDN Not Authorized", "request": {"method": "GET", "header": [{"key": "Content-Type", "value": "application/json"}], "url": {"raw": "{{baseURL}}/api/msimapi/getDetailDevice?msisdn=**********", "host": ["{{baseURL}}"], "path": ["api", "m<PERSON><PERSON><PERSON>", "getDetailDevice"], "query": [{"key": "msisdn", "value": "**********"}]}, "description": "Test Case 40: m<PERSON>dn không đ<PERSON><PERSON><PERSON> phân quyền - Expected: <PERSON><PERSON><PERSON>"}}]}]}